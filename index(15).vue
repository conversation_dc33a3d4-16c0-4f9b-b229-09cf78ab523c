<route lang="yaml">
meta:
  title: 主页
  icon: ant-design:home-twotone
</route>

<script setup lang="ts">
import apiMain from '@/api/modules/main'

// 列表
const loading = ref(false)
const totalArray = ref([])
function getDataList() {
  loading.value = true

  apiMain.index().then((res: any) => {
    loading.value = false
    totalArray.value = res.data
  })
}
const baseUrl = ref(import.meta.env.VITE_APP_BASEURL)
const helpList = ref([
  {
    url: `${baseUrl.value}/doc/project.docx`,
    name: '湖北省高标准农田建设监测监管平台-项目管理',
    time: '2025-08-15'
  },
  {
    url: `${baseUrl.value}/doc/mp.docx`,
    name: '湖北省高标准农田建设监测监管平台-摸排管理',
    time: '2025-08-15'
  },
  {
    url: `${baseUrl.value}/doc/kc.docx`,
    name: '湖北省高标准农田建设监测监管平台-工程核查',
    time: '2025-08-15'
  },
  {
    url: `${baseUrl.value}/doc/onemap.docx`,
    name: '湖北省高标准农田建设监测监管平台-一张图',
    time: '2025-08-15'
  },

])
// const chart1Ref = useTemplateRef('chart1Ref')
// const chart2Ref = useTemplateRef('chart2Ref')
// const chart3Ref = useTemplateRef('chart3Ref')
// const chart4Ref = useTemplateRef('chart4Ref')
// let chart1: any
// let chart2: any
// let chart3: any
// let chart4: any

onMounted(() => {
  // initChart1()
  // initChart2()
  // initChart3()
  // initChart4()
  getDataList()
  // window.addEventListener('resize', () => {
  //   chart1.resize()
  //   chart2.resize()
  //   chart3.resize()
  //   chart4.resize()
  // })
})
function upFileHelp(fileUrl, fileName) {
  const a = document.createElement('a')
  a.href = fileUrl // 文件地址
  a.download = fileName || '' // 下载的文件名
  a.click() // 模拟点击下载
}
// function initChart1() {
//   chart1 = Echarts.init(chart1Ref.value)
//   // 配置数据
//   const option = {
//     tooltip: {
//       trigger: 'axis',
//       axisPointer: { // 坐标轴指示器，坐标轴触发有效
//         type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
//       },
//     },
//     legend: {
//       data: ['直接访问', '邮件营销', '联盟广告', '视频广告', '搜索引擎', '百度', '谷歌', '必应', '其他'],
//     },
//     grid: {
//       left: '3%',
//       right: '4%',
//       bottom: '3%',
//       containLabel: true,
//     },
//     xAxis: [
//       {
//         type: 'category',
//         data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
//       },
//     ],
//     yAxis: [
//       {
//         type: 'value',
//       },
//     ],
//     series: [
//       {
//         name: '直接访问',
//         type: 'bar',
//         data: [320, 332, 301, 334, 390, 330, 320],
//       },
//       {
//         name: '邮件营销',
//         type: 'bar',
//         stack: '广告',
//         data: [120, 132, 101, 134, 90, 230, 210],
//       },
//       {
//         name: '联盟广告',
//         type: 'bar',
//         stack: '广告',
//         data: [220, 182, 191, 234, 290, 330, 310],
//       },
//       {
//         name: '视频广告',
//         type: 'bar',
//         stack: '广告',
//         data: [150, 232, 201, 154, 190, 330, 410],
//       },
//       {
//         name: '搜索引擎',
//         type: 'bar',
//         data: [862, 1018, 964, 1026, 1679, 1600, 1570],
//         markLine: {
//           lineStyle: {
//             type: 'dashed',
//           },
//           data: [
//             [{ type: 'min' }, { type: 'max' }],
//           ],
//         },
//       },
//       {
//         name: '百度',
//         type: 'bar',
//         barWidth: 5,
//         stack: '搜索引擎',
//         data: [620, 732, 701, 734, 1090, 1130, 1120],
//       },
//       {
//         name: '谷歌',
//         type: 'bar',
//         stack: '搜索引擎',
//         data: [120, 132, 101, 134, 290, 230, 220],
//       },
//       {
//         name: '必应',
//         type: 'bar',
//         stack: '搜索引擎',
//         data: [60, 72, 71, 74, 190, 130, 110],
//       },
//       {
//         name: '其他',
//         type: 'bar',
//         stack: '搜索引擎',
//         data: [62, 82, 91, 84, 109, 110, 120],
//       },
//     ],
//   }
//   // 传入数据
//   chart1.setOption(option)
// }
// function initChart2() {
//   chart2 = Echarts.init(chart2Ref.value)
//   // 配置数据
//   const option = {
//     title: {
//       text: '堆叠区域图',
//     },
//     tooltip: {
//       trigger: 'axis',
//       axisPointer: {
//         type: 'cross',
//         label: {
//           backgroundColor: '#6a7985',
//         },
//       },
//     },
//     legend: {
//       data: ['邮件营销', '联盟广告', '视频广告', '直接访问', '搜索引擎'],
//     },
//     toolbox: {
//       feature: {
//         saveAsImage: {},
//       },
//     },
//     grid: {
//       left: '3%',
//       right: '4%',
//       bottom: '3%',
//       containLabel: true,
//     },
//     xAxis: [
//       {
//         type: 'category',
//         boundaryGap: false,
//         data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
//       },
//     ],
//     yAxis: [
//       {
//         type: 'value',
//       },
//     ],
//     series: [
//       {
//         name: '邮件营销',
//         type: 'line',
//         stack: '总量',
//         areaStyle: {},
//         data: [120, 132, 101, 134, 90, 230, 210],
//       },
//       {
//         name: '联盟广告',
//         type: 'line',
//         stack: '总量',
//         areaStyle: {},
//         data: [220, 182, 191, 234, 290, 330, 310],
//       },
//       {
//         name: '视频广告',
//         type: 'line',
//         stack: '总量',
//         areaStyle: {},
//         data: [150, 232, 201, 154, 190, 330, 410],
//       },
//       {
//         name: '直接访问',
//         type: 'line',
//         stack: '总量',
//         areaStyle: {},
//         data: [320, 332, 301, 334, 390, 330, 320],
//       },
//       {
//         name: '搜索引擎',
//         type: 'line',
//         stack: '总量',
//         areaStyle: {},
//         data: [820, 932, 901, 934, 1290, 1330, 1320],
//       },
//     ],
//   }
//   // 传入数据
//   chart2.setOption(option)
// }
// function initChart3() {
//   chart3 = Echarts.init(chart3Ref.value)
//   // 配置数据
//   const option = {
//     title: {
//       text: '摸排情况',
//       subtext: '当前数据',
//       left: 'center',
//     },
//     tooltip: {
//       trigger: 'item',
//       formatter: '{a} <br/>{b} : {c} ({d}%)',
//     },
//     legend: {
//       orient: 'vertical',
//       left: 'left',
//       data: ['局部功能缺失', '整体丧失功能', '审计发现问题', '财政发现问题', '其它'],
//     },
//     series: [
//       {
//         name: '局部功能缺失',
//         type: 'pie',
//         radius: '55%',
//         center: ['50%', '60%'],
//         data: [
//           { value: 335, name: '局部功能缺失' },
//           { value: 310, name: '整体丧失功能' },
//           { value: 234, name: '审计发现问题' },
//           { value: 135, name: '财政发现问题' },
//           { value: 15, name: '其它' },
//         ],
//         emphasis: {
//           itemStyle: {
//             shadowBlur: 10,
//             shadowOffsetX: 0,
//             shadowColor: 'rgba(0, 0, 0, 0.5)',
//           },
//         },
//       },
//     ],
//   }
//   // 传入数据
//   chart3.setOption(option)
// }
// function initChart4() {
//   chart4 = Echarts.init(chart4Ref.value)
//   // 配置数据
//   const option = {
//     title: {
//       text: '基础雷达图',
//     },
//     legend: {
//       data: ['预算分配（Allocated Budget）', '实际开销（Actual Spending）'],
//     },
//     radar: {
//       // shape: 'circle',
//       indicator: [
//         { name: '销售（sales）', max: 6500 },
//         { name: '管理（Administration）', max: 16000 },
//         { name: '信息技术（Information Techology）', max: 30000 },
//         { name: '客服（Customer Support）', max: 38000 },
//         { name: '研发（Development）', max: 52000 },
//         { name: '市场（Marketing）', max: 25000 },
//       ],
//     },
//     series: [{
//       name: '预算 vs 开销（Budget vs spending）',
//       type: 'radar',
//       data: [
//         {
//           value: [4200, 3000, 20000, 35000, 50000, 18000],
//           name: '预算分配（Allocated Budget）',
//         },
//         {
//           value: [5000, 14000, 28000, 26000, 42000, 21000],
//           name: '实际开销（Actual Spending）',
//         },
//       ],
//     }],
//   }
//   // 传入数据
//   chart4.setOption(option)
// }
</script>

<template>
  <div loading="loading">
    <!-- <FaPageHeader>
      <template #title>
        <div class="flex items-center gap-4">
          欢迎使用湖北省高标准农田建设监测监管平台
        </div>
      </template>
    </FaPageHeader> -->
    <!-- title-tips="仅统计近一小时的活跃用户数量" -->
    <div class="mb-4 w-full flex flex-center gap-4 px-4" style="margin-top: 15px;">
      <FaDigitalCard title="项目数量" icon="i-mynaui:box" :digital="totalArray.projectTotal" trend="up" description="当前账号管理项目数量" />
      <FaDigitalCard title="建设面积(亩)" icon="i-carbon:area" :digital="totalArray.areaTotal" description="当前项目建设总面积" trend="up" />
      <FaDigitalCard title="总投资额度(元)" icon="i-bytesize:creditcard" :digital="totalArray.ztzTotal" description="当前项目建设总投资金额" trend="stable" />
      <FaDigitalCard title="摸排数量" icon="i-carbon:activity" :digital="totalArray.mpTotal" description="摸排总数量" />
    </div>
    <div style="width: 100%;padding: 0 15px;">
     <div class="box-top" >
      <el-card style="width: 35%;padding-bottom: 40px;">
        <template #header>
          <div class="card-header">
            <span>代办事项</span>
          </div>
        </template>
        <div class="one-box">
          <div class="one-item">
           <span>项目管理数量</span> 
            <p>0</p>
          </div>
          <div class="one-item">
           <span>摸排管理数量</span>
             <p>0</p>
          </div>
           <div class="one-item">
            <span>项目核查数量</span>
             <p>0</p>
          </div>
        </div>
      </el-card>

      <el-card style="width: 63%;">
        <template #header>
          <div class="card-header">
            <span>项目摸排情况</span>
          </div>
        </template>
      </el-card>
     </div>
     <div class="box-top" style="margin-top: 15px;">
      <el-card style="width: 35%;">
        <template #header>
          <div class="card-header">
            <span>通知公告</span>
          </div>
        </template>
        <el-table
          :data="helpList"
          border
          style="width: 100%;"
        >
          <el-table-column
            type="index"
            label="序号"
            width="60"
          />
          <el-table-column
            prop="name"
            label="文件名称"
            width="350"
          >
            <template #default="scope">
              <div style="display: flex;align-items: center;">
                <img style="width: 30px;height: 30px;margin-right: 5px;" src="@/assets/images/Word.png" alt="">
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <ElButton type="primary" size="small" plain @click="upFileHelp(scope.row.url, scope.row.name)">
                下 载
              </ElButton>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card style="width: 63%;">
        <template #header>
          <div class="card-header">
            <span>项目建设清单</span>
          </div>
        </template>
         <div class="one-box">
          <!-- <el-card class="one-item">
            摸排总数
            <p>5347</p>
          </el-card>
          <el-card class="one-item">
            整体丧失功能
             <p>3123</p>
          </el-card>
           <el-card class="one-item">
            局部丧失功能
             <p>4123</p>
          </el-card>
          <el-card class="one-item">
            已整改
             <p>4123</p>
          </el-card> -->
        </div>
      </el-card>
     </div>
      <!-- <FaPageMain title="柱状图" class="m-0!">
        <div ref="chart1Ref" class="h-[400px] w-full" />
      </FaPageMain>
      <FaPageMain title="折线图" class="m-0!">
        <div ref="chart2Ref" class="h-[400px] w-full" />
      </FaPageMain>
      <FaPageMain title="摸排情况" class="m-0!">
        <div ref="chart3Ref" class="h-[400px] w-full" />
      </FaPageMain>
      <FaPageMain title="雷达图" class="m-0!">
        <div ref="chart4Ref" class="h-[400px] w-full" />
      </FaPageMain> -->
    </div>
  </div>
</template>

<style scoped>
.text-emphasis {
  text-emphasis-style: "❤";
}

.title-info {
  --uno: flex items-center gap-4;

  img {
    --uno: block w-12 h-12;
  }

  h1 {
    --uno: m-0 text-2xl;
  }

  h2 {
    --uno: m-0 text-base text-secondary-foreground/50 font-normal;
  }
}

.qa {
  --uno: m-0 pl-6 text-secondary-foreground/50 list-disc;

  li {
    --uno: mb-2 lh-6 last-mb-0;
  }

  span {
    --uno: text-secondary-foreground font-bold;
  }
}

.one-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.one-item {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 45%;
  padding: 10px 15px;
  margin-bottom: 10px;
  background-color: #ededed;
  border-radius: 3px;
}

.one-item p {
  font-size: 20px;
  font-weight: 700;
}

.one-item span {
  font-size: 16px;
}

.box-top {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>
